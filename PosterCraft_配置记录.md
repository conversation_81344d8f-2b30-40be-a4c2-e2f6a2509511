# PosterCraft 成功配置记录

## 📅 配置日期
2025-07-08

## ✅ 成功配置状态

### 应用信息
- **访问地址**: http://localhost:7860
- **启动命令**: `source postercraft_official/bin/activate && python demo_gradio.py`
- **运行状态**: ✅ 正常运行
- **支持语言**: 中文和英文提示词

### 永久模型存储配置
- **存储路径**: `/Users/<USER>/AI_Models/PosterCraft/`
- **环境变量**:
  ```bash
  export HF_HOME="/Users/<USER>/AI_Models/PosterCraft"
  export HUGGINGFACE_HUB_CACHE="/Users/<USER>/AI_Models/PosterCraft"
  export TRANSFORMERS_CACHE="/Users/<USER>/AI_Models/PosterCraft"
  ```

### 已安装模型
```
/Users/<USER>/AI_Models/PosterCraft/
├── models--black-forest-labs--FLUX.1-dev/
├── models--black-forest-labs--FLUX.1-schnell/
├── models--Qwen--Qwen2.5-3B/
├── models--Qwen--Qwen2.5-3B-Instruct/
├── models--Qwen--Qwen3-8B/
└── postercraft_config.py
```

### 当前配置文件 (demo_gradio.py)
```python
# 关键配置
DEFAULT_PIPELINE_PATH = "/Users/<USER>/AI_Models/PosterCraft/models--black-forest-labs--FLUX.1-schnell/snapshots/741f7c3ce8b383c54771c7003378a50191e9efe9"
DEFAULT_QWEN_MODEL_PATH = None  # 暂时禁用，模型文件有问题
DEFAULT_CUSTOM_WEIGHTS_PATH = get_model_path("postercraft_v1_rl")

# 启动配置
server_port=7860
```

## 🚀 启动步骤

1. **进入项目目录**:
   ```bash
   cd /Users/<USER>/PosterCraft
   ```

2. **激活虚拟环境**:
   ```bash
   source postercraft_official/bin/activate
   ```

3. **启动应用**:
   ```bash
   python demo_gradio.py
   ```

4. **访问应用**:
   打开浏览器访问 http://localhost:7860

## ⚙️ 功能状态

### ✅ 正常功能
- 海报生成 (FLUX模型)
- 中文/英文提示词支持
- 参数调节 (尺寸、步数、种子等)
- Web界面交互

### ⚠️ 暂时禁用功能
- **Prompt Recap功能**: Qwen模型有问题，已禁用
- **解决方案**: 使用时取消勾选"Enable Prompt Recap"

## 🔧 依赖环境

### Python环境
- **虚拟环境**: postercraft_official/
- **Python版本**: 3.11
- **关键依赖**: 已安装protobuf

### 系统要求
- **运行模式**: CPU (CUDA不可用)
- **内存**: 需要足够内存加载FLUX模型

## 📝 使用说明

### 基本使用
1. 在"Prompt"框输入描述 (支持中文)
2. 确保"Enable Prompt Recap"未勾选
3. 调整参数 (可选)
4. 点击"Generate Image"

### 推荐设置
- **尺寸**: 832x1216 (默认)
- **推理步数**: 28
- **引导比例**: 3.5
- **种子**: 留空或-1 (随机)

## 🐛 已知问题及解决方案

### 问题1: Qwen模型错误
- **现象**: "Recap is enabled, but the recap model is not available"
- **解决**: 取消勾选"Enable Prompt Recap"

### 问题2: 端口占用
- **现象**: "address already in use"
- **解决**: 修改demo_gradio.py中的server_port

### 问题3: protobuf缺失
- **现象**: ImportError protobuf
- **解决**: `pip install protobuf`

## 🔄 重启应用

如果需要重启应用:
1. 在终端按 Ctrl+C 停止
2. 重新运行启动命令

## 📁 重要文件路径

- **项目根目录**: `/Users/<USER>/PosterCraft/`
- **虚拟环境**: `/Users/<USER>/PosterCraft/postercraft_official/`
- **模型存储**: `/Users/<USER>/AI_Models/PosterCraft/`
- **配置文件**: `/Users/<USER>/AI_Models/PosterCraft/postercraft_config.py`
- **主程序**: `/Users/<USER>/PosterCraft/demo_gradio.py`

## 💡 优化建议

1. **性能**: 如有GPU可用，修改device配置
2. **Qwen模型**: 重新下载完整的Qwen模型以启用Recap功能
3. **端口**: 可根据需要修改默认端口7860

---
**配置完成时间**: 2025-07-08 22:35
**状态**: ✅ 可正常使用
