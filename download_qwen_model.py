#!/usr/bin/env python3
"""
下载Qwen模型到永久存储位置
"""
import os
import sys
sys.path.append('/Users/<USER>/AI_Models/PosterCraft')
from postercraft_config import setup_environment

# 设置环境
setup_environment()

from transformers import AutoTokenizer, AutoModelForCausalLM

def download_qwen_model():
    model_name = "Qwen/Qwen2.5-3B-Instruct"
    print(f"🔄 开始下载模型: {model_name}")
    
    try:
        # 下载tokenizer
        print("📥 下载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("✅ Tokenizer下载完成")
        
        # 下载模型
        print("📥 下载模型文件...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype="auto",
            device_map="cpu"  # 强制使用CPU避免内存问题
        )
        print("✅ 模型下载完成")
        
        print(f"✅ 模型已保存到: {os.environ.get('HF_HOME')}")
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = download_qwen_model()
    if success:
        print("🎉 模型下载成功！")
    else:
        print("💥 模型下载失败！")
